"use client"

import * as React from "react"
import Image from "next/image"
import { Share2, <PERSON><PERSON><PERSON>, DollarSign, ChevronUp, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DashboardMatch } from "@/lib/betexplorer-types"

interface PositionsTableProps {
  className?: string
  // Props antigas (para compatibilidade)
  gameTitle?: string
  league?: string
  time?: string
  // Props novas (dados reais)
  matchData?: DashboardMatch
  loading?: boolean
  error?: string | null
  onRefresh?: () => void
}

export default function PositionsTable({
  className,
  // Props antigas (fallback)
  gameTitle = "Real Madrid x Inter de Milão",
  league = "Champions League",
  // Props novas
  matchData,
  loading = false,
  error = null
}: PositionsTableProps) {
  const [activeMarket, setActiveMarket] = React.useState("1X2")
  const [isCollapsed, setIsCollapsed] = React.useState(true) // Recolhido por padrão

  // Determinar dados a serem exibidos (priorizar matchData)
  const displayData = React.useMemo(() => {
    if (matchData) {
      console.log('🎯 PositionsTable recebeu matchData:', {
        homeTeam: matchData.homeTeam,
        awayTeam: matchData.awayTeam,
        score: matchData.score,
        minute: matchData.minute,
        finished: matchData.finished,
        isLive: matchData.isLive,
        hasHomeLogo: !!matchData.homeTeamLogo,
        hasAwayLogo: !!matchData.awayTeamLogo
      })

      return {
        homeTeam: matchData.homeTeam,
        awayTeam: matchData.awayTeam,
        homeTeamLogo: matchData.homeTeamLogo,
        awayTeamLogo: matchData.awayTeamLogo,
        score: matchData.score,
        minute: matchData.minute,
        finished: matchData.finished,
        competition: matchData.competition,
        country: matchData.country,
        isLive: matchData.isLive,
        odds: matchData.odds
      }
    }

    // Fallback para dados estáticos
    const teams = gameTitle.split(' x ')
    return {
      homeTeam: teams[0]?.trim() || 'Time Casa',
      awayTeam: teams[1]?.trim() || 'Time Visitante',
      homeTeamLogo: undefined,
      awayTeamLogo: undefined,
      score: '0:0',
      minute: 0,
      finished: false,
      competition: league,
      country: '',
      isLive: false,
      odds: {}
    }
  }, [matchData, gameTitle, league])

  // Função para detectar se o jogo está no intervalo
  const isHalfTime = React.useMemo(() => {
    if (!displayData.competition) return false
    return displayData.competition.toLowerCase().includes('half-time')
  }, [displayData.competition])

  // Função para detectar se o jogo está em tempo extra
  const isExtraTime = React.useMemo(() => {
    if (!displayData.competition) return false
    return displayData.competition.toLowerCase().includes('extra time')
  }, [displayData.competition])

  // Função para obter o escudo do time
  const getTeamShield = (teamName: string) => {
    const lowerName = teamName.toLowerCase()
    console.log('Checking team:', teamName, 'lowercase:', lowerName) // Debug
    if (lowerName.includes('real madrid')) {
      console.log('Found Real Madrid shield') // Debug
      return '/real madrid.png'
    }
    if (lowerName.includes('inter')) {
      console.log('Found Inter shield') // Debug
      return '/inter shield.png'
    }
    // Para outros times, retornamos um placeholder ou null
    // Você pode adicionar mais escudos conforme necessário
    console.log('No shield found for:', teamName) // Debug
    return null
  }

  // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME
  const renderGameTitleWithShields = () => {
    const homeTeam = displayData.homeTeam
    const awayTeam = displayData.awayTeam
    const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam)
    const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam)

    return (
      <div className="flex items-center gap-3">
        {/* Time da casa: NOME > ESCUDO */}
        <div className="min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-start justify-start gap-1 overflow-hidden">
          <span className="text-xs  text-foreground">
            {homeTeam}
          </span>
          {homeShield && (
            <Image
              src={homeShield}
              alt={homeTeam}
              width={24}
              height={24}
              className="rounded-sm"
            />
          )}
        </div>

        {/* Placar e status do jogo */}
        <div className="flex flex-col items-center mx-4">
          <span className="text-xs font-semibold text-muted-foreground">
            {displayData.score || '0:0'}
          </span>
          {isHalfTime && (
            <span className="text-xs text-orange-500 font-medium">
              HT
            </span>
          )}
          {isExtraTime && (
            <span className="text-xs text-blue-500 font-medium">
              {displayData.minute}&apos;
            </span>
          )}
          {displayData.isLive && !isHalfTime && !isExtraTime && (
            <span className="text-xs text-green-500 font-medium">
              {displayData.minute}&apos;
            </span>
          )}
          {displayData.finished && (
            <span className="text-xs text-muted-foreground">
              FT
            </span>
          )}
        </div>

        {/* Time visitante: ESCUDO < NOME */}
        <div className="min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-start justify-start gap-1 overflow-hidden">
          {awayShield && (
            <Image
              src={awayShield}
              alt={awayTeam}
              width={24}
              height={24}
              className="rounded-sm"
            />
          )}
          <span className="text-xs  text-foreground">
            {awayTeam}
          </span>
        </div>
      </div>
    )
  }

  const markets = [
    { id: "1X2", label: "1X2 Stats" },
    { id: "OU", label: "O/U" },
    { id: "AH", label: "AH" },
    { id: "DNB", label: "DNB" },
    { id: "DC", label: "DC" },
    { id: "BTTS", label: "BTTS" },
  ]

  return (
    <div className={`bg-background flex flex-col h-full ${className}`}>
      {/* Container da tabela - sempre visível mas com conteúdo condicional */}
      <div className={`${isCollapsed ? 'flex-shrink-0' : 'flex-1'} m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar`}>

        {/* Header com grid de 3 colunas para estado e jogos + botões separados */}
        <div className="px-6 py-1 border-border">
          <div className="flex items-center justify-between min-h-[60px]">

            {/* Grid de 3 colunas: [STATUS] [GAME] [STATUS] */}
            <div className="flex  items-center flex-1">

              {/* Coluna 1: Status do Jogo */}
              <div className="flex justify-start basis-[10%]">
                {(displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && (
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                      displayData.finished
                        ? 'bg-gray-500'
                        : isExtraTime
                          ? 'bg-blue-500 animate-pulse'
                          : isHalfTime
                            ? 'bg-orange-500 animate-pulse'
                            : 'bg-green-500 animate-pulse'
                    }`}></div>
                    <span className={`text-xs font-medium whitespace-nowrap ${
                      displayData.finished
                        ? 'text-gray-500'
                        : isExtraTime
                          ? 'text-blue-500'
                          : isHalfTime
                            ? 'text-orange-500'
                            : 'text-green-500'
                    }`}>
                      {displayData.finished
                        ? 'FINALIZADO'
                        : isExtraTime
                          ? 'TEMPO EXTRA'
                          : isHalfTime
                            ? 'INTERVALO'
                            : 'AO VIVO'}
                    </span>
                  </div>
                )}
              </div>

              {/* Coluna 2: Container do Jogo (Centralizado) */}
            
              <div className="max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center">
                {renderGameTitleWithShields()}
                <p className="text-sm text-muted-foreground mt-1">
                  {(() => {
                    const filterGameInfo = (text: string) => {
                      if (!text) return false;
                      const lowerText = text.toLowerCase();
                      return !lowerText.includes('half') &&
                             !lowerText.includes('tempo') &&
                             !lowerText.includes('1st') &&
                             !lowerText.includes('2nd') &&
                             !lowerText.includes('1º') &&
                             !lowerText.includes('2º') &&
                             !lowerText.includes('primeiro') &&
                             !lowerText.includes('segundo') &&
                             !lowerText.includes('finished') &&
                             !lowerText.includes('extra time');
                    };

                    const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';
                    const country = filterGameInfo(displayData.country) ? displayData.country : '';

                    return (
                      <>
                        {competition}
                        {competition && country && ' • '}
                        {country}
                      </>
                    );
                  })()}
                  {loading && ' • Carregando...'}
                  {error && ' • Erro ao carregar'}
                </p>
              </div>

              {/* Coluna 3: Vazia (para equilibrar o grid) */}
              <div></div>
            </div>

            {/* Botões de Ação (fora do grid) */}
            <div className="flex items-center gap-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de compartilhar */}}
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de configurações */}}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de dólar */}}
              >
                <DollarSign className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Conteúdo da tabela - só visível quando expandido */}
        {!isCollapsed && (
          <>
            {/* Toggles de Mercados */}
            <div className="px-6 py-4 border-b border-border">
              <div className="flex items-center gap-2">
                {markets.map((market) => (
                  <button
                    key={market.id}
                    onClick={() => setActiveMarket(market.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeMarket === market.id
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground"
                    }`}
                  >
                    {market.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tabela */}
            <table className="w-full text-sm">
              <thead className=" backdrop-blur-sm">
                <tr>
                  <th className="text-left p-3 font-medium text-muted-foreground">Type</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Size (ETH)</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Entry Price</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Margin Required</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Margin Weight</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Mark Price</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">PnL</th>
                  <th className="text-left p-3 font-medium text-muted-foreground">Status</th>
                </tr>
              </thead>
              <tbody>
                {/* Linhas vazias para demonstração */}
                {Array.from({ length: 8 }).map((_, index) => (
                  <tr key={index} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                    <td className="p-3 text-muted-foreground">-</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </>
        )}
      </div>

    
    </div>
  )
}
